import type React from 'react';
import { useEffect } from 'react';
import { LogBox } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import Config from 'react-native-config';
import { configureReanimatedLogger, ReanimatedLogLevel } from 'react-native-reanimated';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import { PersistGate } from 'redux-persist/integration/react';
import * as Sentry from '@sentry/react-native';
import { Provider } from 'react-redux';
import { toastConfig } from '@/src/components/CustomToast/config';
import ErrorBoundary from '@/src/components/ErrorBoundary';
import { persistor, store } from '@/src/redux/store';
import { navigationRef } from '@/src/utilities/navigation';
import AppNavigator from '@/src/navigation';
import '@/styles/global.css';
import OfflineIndicator from './src/components/OfflineIndicator';
import { useDeepLink } from './src/deeplink/useDeepLink';
import useStorage from './src/hooks/storage';

LogBox.ignoreAllLogs();

Sentry.init({
  dsn: Config.SENTRY_DSN_URL,
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1,
  integrations: [Sentry.mobileReplayIntegration()],
});

configureReanimatedLogger({ level: ReanimatedLogLevel.warn, strict: false });

const NavigationContent = (): React.JSX.Element => {
  useDeepLink();

  return (
    <SafeAreaProvider>
      <AppNavigator />
      <Toast config={toastConfig} />
    </SafeAreaProvider>
  );
};

const AppContent = (): React.JSX.Element => {
  return (
    <NavigationContainer ref={navigationRef}>
      <NavigationContent />
    </NavigationContainer>
  );
};

const App = (): React.JSX.Element => {
  const { clearAllStorage } = useStorage();

  const handleError = (error: Error, errorInfo: any) => {
    Sentry.captureException(error, {
      contexts: {
        react: {
          componentStack: errorInfo.componentStack,
        },
      },
    });
  };

  useEffect(() => {
    // clearAllStorage()
  }, []);

  return (
    <OfflineIndicator>
      <ErrorBoundary onError={handleError}>
        <Provider store={store}>
          <PersistGate persistor={persistor} loading={null}>
            <AppContent />
          </PersistGate>
        </Provider>
      </ErrorBoundary>
    </OfflineIndicator>
  );
};

export default Sentry.wrap(App);
