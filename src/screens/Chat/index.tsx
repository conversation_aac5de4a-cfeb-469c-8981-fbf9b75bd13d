import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  ScrollView,
  Pressable,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import BackButton from '@/src/components/BackButton';
import UserAvatar from '@/src/components/UserAvatar';
import { formatMessageTime } from '@/src/utilities/datetime';
import Send from '@/src/assets/svgs/Send';
import MoreOptions from './components/MoreOptions';
import ReplyPreview from './components/ReplyPreview';
import { MessageI } from './components/ReplyPreview/types';
import SwipeableMessage from './components/SwipeableMessage';

const ChatScreen = () => {
  const navigation = useNavigation();
  const [messageText, setMessageText] = useState('');
  const [messages, setMessages] = useState<MessageI[]>([
    {
      id: 1,
      text: 'Hey, how are you doing?',
      from: 'other',
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      user: {
        id: 'other',
        name: '<PERSON> Doe',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      },
    },
    {
      id: 2,
      text: "I'm good! Thanks for asking",
      from: 'me',
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000).toISOString(),
      user: { id: 'me', name: 'You' },
    },
    {
      id: 3,
      text: 'Are we still meeting tomorrow?',
      from: 'other',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      user: {
        id: 'other',
        name: 'John Doe',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      },
    },
    {
      id: 4,
      text: 'Yes, see you at 3 PM!',
      from: 'me',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000 + 10 * 60 * 1000).toISOString(),
      user: { id: 'me', name: 'You' },
    },
    {
      id: 5,
      text: 'Good morning!',
      from: 'other',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      user: {
        id: 'other',
        name: 'John Doe',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      },
    },
    {
      id: 6,
      text: 'Morning! Ready for our meeting?',
      from: 'me',
      timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      user: { id: 'me', name: 'You' },
    },
  ]);

  const [replyPreview, setReplyPreview] = useState<MessageI | null>(null);
  const [selectedMessage, setSelectedMessage] = useState<MessageI | null>(null);
  const [optionsVisible, setOptionsVisible] = useState(false);

  const scrollRef = useRef<ScrollView>(null);

  const onBack = () => {
    navigation.goBack();
  };

  const handleSend = () => {
    if (messageText.trim() === '') return;

    const now = new Date();
    const newMessage: MessageI = {
      id: Date.now(),
      text: messageText.trim(),
      from: 'me' as const,
      timestamp: now.toISOString(),
      user: { id: 'me', name: 'You' },
      replyTo: replyPreview || undefined,
    };

    setMessages((prev) => [...prev, newMessage]);
    setMessageText('');
    setReplyPreview(null);

    setTimeout(() => {
      scrollRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const handleMessageLongPress = (message: MessageI) => {
    setSelectedMessage(message);
    setOptionsVisible(true);
  };

  const handleReply = () => {
    if (selectedMessage) {
      setReplyPreview(selectedMessage);
    }
    setOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleDelete = () => {
    if (selectedMessage) {
      setMessages((prev) => prev.filter((msg) => msg.id !== selectedMessage.id));
    }
    setOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleCloseOptions = () => {
    setOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleCloseReply = () => {
    setReplyPreview(null);
  };

  const handleSwipeReply = (message: MessageI) => {
    setReplyPreview(message);
  };

  const getDateLabel = (timestamp: string): string => {
    const messageDate = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const messageDateOnly = new Date(
      messageDate.getFullYear(),
      messageDate.getMonth(),
      messageDate.getDate(),
    );
    const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const yesterdayOnly = new Date(
      yesterday.getFullYear(),
      yesterday.getMonth(),
      yesterday.getDate(),
    );

    if (messageDateOnly.getTime() === todayOnly.getTime()) {
      return 'Today';
    } else if (messageDateOnly.getTime() === yesterdayOnly.getTime()) {
      return 'Yesterday';
    } else {
      return messageDate.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'short',
        day: 'numeric',
      });
    }
  };

  const groupMessagesByDate = (messages: MessageI[]) => {
    const groups: { [key: string]: MessageI[] } = {};

    const sortedMessages = [...messages].sort(
      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
    );

    sortedMessages.forEach((message) => {
      const dateLabel = getDateLabel(message.timestamp);
      if (!groups[dateLabel]) {
        groups[dateLabel] = [];
      }
      groups[dateLabel].push(message);
    });

    return groups;
  };

  const messageGroups = groupMessagesByDate(messages);

  return (
    <SafeAreaView className="flex-1 bg-white" edges={['top', 'left', 'right']}>
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <View className="flex-row items-center gap-2 px-2  border-b border-gray-200 bg-white z-10">
          <BackButton label="" onBack={onBack} />
          <View className="flex-row items-center gap-2">
            <UserAvatar
              avatarUri={'https://randomuser.me/api/portraits/men/1.jpg'}
              width={37}
              height={37}
            />
            <Text className="text-base font-medium">John Doe</Text>
          </View>
        </View>
        <ScrollView
          ref={scrollRef}
          className="flex-1 px-4"
          contentContainerStyle={{ paddingVertical: 12 }}
          keyboardShouldPersistTaps="handled"
        >
          {Object.entries(messageGroups)
            .sort(([, messagesA], [, messagesB]) => {
              const earliestA = Math.min(...messagesA.map((m) => new Date(m.timestamp).getTime()));
              const earliestB = Math.min(...messagesB.map((m) => new Date(m.timestamp).getTime()));
              return earliestA - earliestB;
            })
            .map(([dateLabel, messagesInGroup]) => (
              <View key={dateLabel}>
                <Text className="text-center text-gray-500 font-semibold my-3">{dateLabel}</Text>

                {messagesInGroup.map((msg) => {
                  const time = formatMessageTime(new Date(msg.timestamp));
                  return (
                    <View key={msg.id} className="mb-2">
                      <SwipeableMessage
                        message={msg}
                        onReply={handleSwipeReply}
                        onLongPress={handleMessageLongPress}
                      >
                        <View
                          className={`max-w-[80%] p-3 rounded-xl ${
                            msg.from === 'me' ? 'bg-violet-100 self-end' : 'bg-gray-100 self-start'
                          }`}
                        >
                          {msg.replyTo && (
                            <View className="mb-2 p-2 bg-white/50 border-l-3 border-[#D2C9FF]">
                              <Text className="text-xs font-medium text-gray-600">
                                {msg.replyTo.user?.name || 'Unknown'}
                              </Text>
                              <Text className="text-xs text-gray-700" numberOfLines={1}>
                                {msg.replyTo.text}
                              </Text>
                            </View>
                          )}
                          <Text className="text-sm text-gray-900">{msg.text}</Text>
                          <Text className="text-xs text-gray-400 mt-1 text-right">{time}</Text>
                        </View>
                      </SwipeableMessage>
                    </View>
                  );
                })}
              </View>
            ))}
        </ScrollView>

        <View className="border-t border-gray-200 bg-white py-1">
          {replyPreview && <ReplyPreview message={replyPreview} onClose={handleCloseReply} />}
          <View className="flex-row items-center px-3 py-2">
            <View className="flex-1 flex-row items-center border border-borderLight bg-whitePink rounded-full px-3 py-1 max-h-24">
              <TextInput
                value={messageText}
                onChangeText={setMessageText}
                placeholder={replyPreview ? 'Reply to message...' : 'Type a message'}
                placeholderTextColor="#6B7280"
                className="flex-1 text-sm text-gray-900 p-0 max-h-10"
                multiline
                scrollEnabled
                textAlignVertical="top"
                style={{
                  paddingVertical: Platform.OS === 'android' ? 4 : 8,
                }}
              />
              <Pressable className="p-2" onPress={handleSend}>
                <Send color="#448600" width={2} height={2} />
              </Pressable>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
      <MoreOptions
        onClose={handleCloseOptions}
        optionsVisible={optionsVisible}
        onDelete={handleDelete}
        onReply={handleReply}
      />
    </SafeAreaView>
  );
};

export default ChatScreen;
