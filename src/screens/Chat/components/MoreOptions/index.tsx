/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import BottomSheet from '@/src/components/Bottomsheet/index';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import Send from '@/src/assets/svgs/Send';
import TrashBin from '@/src/assets/svgs/TrashBin';
import { MoreOptionsPropsI } from './types';

const MoreOptions = (props: MoreOptionsPropsI) => {
  const { onClose, onDelete, onReply, optionsVisible } = props;
  return (
    <BottomSheet
      onModalHide={() => {}}
      height={RFPercentage(20)}
      visible={optionsVisible}
      onClose={onClose}
    >
      <OptionsMenu>
        <OptionItem
          icon={<Send color="#000000" width={2} height={2} />}
          label="Reply"
          onPress={onReply}
        />
        <View className="h-[1px] bg-gray-200 my-2" />
        <OptionItem
          icon={<TrashBin width={2} height={2} />}
          label="Delete"
          textClassName="text-red-500"
          onPress={onDelete}
        />
      </OptionsMenu>
    </BottomSheet>
  );
};

export default MoreOptions;
