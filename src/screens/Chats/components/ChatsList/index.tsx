/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { FlatList, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { HomeStackParamListI } from '@/src/navigation/types';
import AiBot from '@/src/assets/images/others/aibot.png';
import ChatItem from '../ChatItem';
import TopBanner from '../TopBanner';

export const chatData = [
  {
    id: '4',
    name: 'Team Alpha',
    message: 'Meeting starts in 5 minutes!',
    timestamp: Date.now() - 5 * 60 * 1000,
    avatar: 'https://randomuser.me/api/portraits/men/68.jpg',
    isGroup: true,
  },
  {
    id: '2',
    name: '<PERSON>',
    message: 'Great!',
    timestamp: Date.now() - 20 * 60 * 1000,
    avatar: 'https://randomuser.me/api/portraits/men/77.jpg',
    isGroup: true,
  },
  {
    id: '8',
    name: '<PERSON>',
    message: 'Got the files.',
    timestamp: Date.now() - 40 * 60 * 1000,
    avatar: 'https://randomuser.me/api/portraits/men/89.jpg',
    isGroup: false,
  },
  {
    id: '1',
    name: 'Jack Doe',
    message: 'Hi there! How are you?',
    timestamp: Date.now() - 5 * 60 * 60 * 1000,
    avatar: 'https://randomuser.me/api/portraits/men/76.jpg',
    isGroup: false,
  },
  {
    id: '7',
    name: 'Marketing Team',
    message: 'Campaign approved!',
    timestamp: Date.now() - 4 * 60 * 60 * 1000,
    avatar: 'https://randomuser.me/api/portraits/men/12.jpg',
    isGroup: true,
  },
  {
    id: '10',
    name: 'Project Beta',
    message: 'Final draft uploaded.',
    timestamp: Date.now() - 7 * 60 * 60 * 1000,
    avatar: 'https://randomuser.me/api/portraits/men/50.jpg',
    isGroup: true,
  },
  {
    id: '5',
    name: 'Bob Marley',
    message: 'No worries!',
    timestamp: Date.now() - 12 * 60 * 60 * 1000,
    avatar: 'https://randomuser.me/api/portraits/men/33.jpg',
    isGroup: false,
  },
  {
    id: '3',
    name: 'Alice Smith',
    message: 'Can we talk tomorrow?',
    timestamp: Date.now() - 26 * 60 * 60 * 1000,
    avatar: 'https://randomuser.me/api/portraits/women/65.jpg',
    isGroup: false,
  },
  {
    id: '6',
    name: 'Eve Watson',
    message: 'Call me when you’re free. This is long long long text',
    timestamp: Date.now() - 2 * 24 * 60 * 60 * 1000,
    avatar: 'https://randomuser.me/api/portraits/women/72.jpg',
    isGroup: false,
  },
  {
    id: '9',
    name: 'Laura Kinney',
    message: 'See you soon!',
    timestamp: Date.now() - 10 * 24 * 60 * 60 * 1000,
    avatar: 'https://randomuser.me/api/portraits/women/51.jpg',
    isGroup: false,
  },
];

const ChatsList = () => {
  const navigation = useNavigation<NativeStackNavigationProp<HomeStackParamListI>>();
  return (
    <View className="flex-1 bg-white">
      <FlatList
        data={chatData}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <ChatItem item={item} />}
        ItemSeparatorComponent={() => <View className="h-px bg-borderGrayLight" />}
        ListHeaderComponent={
          <View>
            <TopBanner
              title="Chat with Navicater AI"
              subtitle="Get all your questions answered and find relevant chats easily"
              iconSource={AiBot}
              onPress={() => navigation.navigate('AIChat')}
            />
          </View>
        }
      />
    </View>
  );
};

export default ChatsList;
