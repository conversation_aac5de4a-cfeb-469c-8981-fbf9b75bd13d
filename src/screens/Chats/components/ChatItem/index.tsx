import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import UserAvatar from '@/src/components/UserAvatar';
import { formatSocialTime } from '@/src/utilities/datetime';
import { BottomTabNavigationI } from '@/src/navigation/types';

const ChatItem = ({
  item,
}: {
  item: { id: string; avatar: string; name: string; message: string; timestamp: number };
}) => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const onPress = () => {
    navigation.navigate('Chat', {
      id: item.id,
    });
  };
  return (
    <Pressable onPress={onPress}>
      <View className="flex-row items-center justify-between p-3">
        <UserAvatar avatarUri={item.avatar} width={50} height={50} name={item.name} />
        <View className="flex-1 px-3">
          <Text className="text-base font-semibold text-black">{item.name}</Text>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            className="text-sm text-gray-500 mt-0.5 w-full"
          >
            {item.message}
          </Text>
        </View>
        <Text className="text-xs text-gray-400">{formatSocialTime(item.timestamp)}</Text>
      </View>
    </Pressable>
  );
};

export default ChatItem;
