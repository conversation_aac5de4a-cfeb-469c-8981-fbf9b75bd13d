import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import ChevronRight from '@/src/assets/svgs/ChevronRight';

interface TopBannerProps {
  title: string;
  subtitle: string;
  iconSource?: unknown;
  iconUri?: string;
  titleColor?: string;
  subtitleColor?: string;
  showArrow?: boolean;
  arrowColor?: string;
  onPress?: () => void;
  containerClassName?: string;
  iconClassName?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  arrowClassName?: string;
}

const TopBanner: React.FC<TopBannerProps> = ({
  title,
  subtitle,
  iconSource,
  iconUri,
  titleColor = 'text-gray-900',
  subtitleColor = 'text-labelGray',
  showArrow = true,
  onPress,
  containerClassName = '',
  iconClassName = '',
  titleClassName = '',
  subtitleClassName = '',
  arrowClassName = '',
}) => {
  const BannerContent = () => (
    <LinearGradient
      colors={['rgba(242, 249, 235, 0.6)', 'rgba(221, 239, 200, 0.7)']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      className="shadow-banner"
    >
      <View className={`flex-row items-start justify-end  px-2.5 py-4 ${containerClassName}`}>
        {(iconSource || iconUri) && (
          <View className={`${iconClassName}`}>
            <Image
              source={iconSource || { uri: iconUri }}
              className="w-12 h-12 rounded-full"
              resizeMode="contain"
            />
          </View>
        )}

        <View className="flex-1 px-5 gap-2">
          <Text className={`text-lg font-medium ${titleColor} ${titleClassName}`}>{title}</Text>
          <Text className={`text-sm leading-5 ${subtitleColor} ${subtitleClassName}`}>
            {subtitle}
          </Text>
        </View>

        {showArrow && (
          <View className={`${arrowClassName}`}>
            <ChevronRight />
          </View>
        )}
      </View>
    </LinearGradient>
  );

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        <BannerContent />
      </TouchableOpacity>
    );
  }

  return <BannerContent />;
};

export default TopBanner;
