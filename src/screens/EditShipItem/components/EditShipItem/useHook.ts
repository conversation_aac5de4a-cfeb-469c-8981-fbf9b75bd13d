import { useCallback, useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
// import { useForm } from 'react-hook-form';
import {
  useDispatch,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  useSelector,
} from 'react-redux';
import { selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { addShipExperience } from '@/src/redux/slices/experience/experienceSlice';
// import { deleteEducationSkillAsync } from '@/src/redux/slices/education/educationSlice';
import {
  AppDispatch,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  RootState,
} from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { showToast } from '@/src/utilities/toast';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import { fetchShipDetails, fetchSingleShipDetails } from '@/src/networks/experienceShip.ts/ship';
import { EquipmentI } from '@/src/networks/experienceShip.ts/types';
import { formatDateToYMD, generateShipAddEditPayload } from '../utils';
import {
  FieldTypeI,
  ShipDetailsFormDataI,
  ShipPreFilledDataTypeI,
  UseEditShipItemI,
} from './types';

export const useEditShipItem = (
  preFilledData: ShipPreFilledDataTypeI[],
  field: FieldTypeI,
  profileId?: string,
  shipId?: string,
  fromProfileExperience?: {
    id: string;
    imo: string;
  },
  refetch?: () => void,
): UseEditShipItemI => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [localSkills, setLocalSkills] = useState<SearchResultI[]>([]);
  const [initialSkills, setInitialSkills] = useState<SearchResultI[]>([]);
  const skillsSelection = useSelector(selectMultipleSelectionsByKey('skill'));
  const [loading, setLoading] = useState(false);
  const [isPresent, setIsPresent] = useState(false);
  const [isAddVisible, setIsAddVisible] = useState(false);
  const [equipments, setEquipments] = useState<EquipmentI[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const dispatch = useDispatch<AppDispatch>();

  const methods = useForm<ShipDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      imoNumber: {},
      shipName: '',
      shipSize: '',
      shipType: {},
      grossTonnage: '',
      deadWeight: '',
      power: '',
      capacity: '',
      fromDate: '',
      toDate: '',
      additionalDetails: '',
      department: {},
    },
  });

  useEffect(() => {
    if (shipId) {
      try {
        setLoading(true);
        fetchShip(shipId);
      } catch (error) {
        triggerErrorBoundary(
          new Error(
            'Failed to load ship details: ' +
              (error instanceof Error ? error.message : 'Unknown error'),
          ),
        );
      } finally {
        setLoading(false);
      }
    }
  }, [shipId, methods, fromProfileExperience, field]);

  const fetchShip = async (shipId: string) => {
    const response = await fetchSingleShipDetails(shipId);

    if (fromProfileExperience !== undefined) {
      const fetchedShip = {
        imoNumber: { imo: fromProfileExperience.imo },
        shipName: response.name,
        shipType: response.subVesselType,
        grossTonnage: response.sizeGt,
        deadWeight: response?.dwt?.toString(),
        power: response.powerKw,
        fromDate: response.fromDate,
        toDate: response.toDate,
        additionalDetails: response?.details,
        department: response.department,
      };
      if (!response.toDate) {
        setIsPresent(true);
      }
      setLocalSkills((response.skills as unknown as SearchResultI[]) || []);
      setInitialSkills(response.skills);
      methods.reset(fetchedShip);
    } else {
      const matchedShip = field.ships.find(
        (ship) =>
          ship.id === response.id &&
          formatDateToYMD(ship.fromDate) === formatDateToYMD(response.fromDate) &&
          formatDateToYMD(ship.toDate) === formatDateToYMD(response.toDate),
      );
      const imoNumber = matchedShip?.ship ?? null;

      const fetchedShip = {
        imoNumber: imoNumber!,
        shipName: response.name,
        shipType: response.subVesselType,
        grossTonnage: response.sizeGt,
        deadWeight: response?.dwt.toString(),
        power: response.powerKw,
        fromDate: response.fromDate,
        toDate: response.toDate,
        additionalDetails: response?.details,
        department: response.department,
      };
      if (!response.toDate) {
        setIsPresent(true);
      }
      setLocalSkills(response.skills?.length > 0 ? response.skills : []);
      setInitialSkills(response.skills?.length > 0 ? response.skills : []);

      methods.reset(fetchedShip);
    }
    setEquipments(response.equipments);
  };

  const shipReFetch = () => {
    fetchShip(shipId!);
  };

  useEffect(() => {
    if (!skillsSelection) return;

    setLocalSkills((prev) => {
      const existingIds = new Set(prev.map((s) => s.id));
      const merged = [...prev, ...skillsSelection.filter((s) => !existingIds.has(s.id))];
      return merged;
    });
  }, [skillsSelection]);

  useEffect(() => {
    const requiredFieldsFilled =
      (methods.watch('imoNumber')?.imo?.trim() ?? '').length > 0 &&
      (methods.watch('shipName')?.trim() ?? '').length > 0 &&
      (methods.watch('shipType')?.name?.trim() ?? '').length > 0 &&
      (methods.watch('grossTonnage')?.trim() ?? '').length > 0 &&
      (methods.watch('deadWeight')?.toString().trim() ?? '').length > 0 &&
      (methods.watch('department')?.name?.trim() ?? '').length > 0;

    setIsAddVisible(!!requiredFieldsFilled);
  }, [
    methods.watch('imoNumber'),
    methods.watch('shipName'),
    methods.watch('shipType'),
    methods.watch('grossTonnage'),
    methods.watch('deadWeight'),
    methods.watch('department'),
  ]);

  const onSubmit = async (data: ShipDetailsFormDataI) => {
    const payload = generateShipAddEditPayload(
      preFilledData,
      data,
      shipId,
      field,
      localSkills,
      initialSkills,
    );
    try {
      setIsSubmitting(true);
      const response = (await dispatch(
        addShipExperience({ payload }),
      ).unwrap()) as unknown as string[];

      showToast({
        message: 'Success',
        description: `Added new Ship successfully`,
        type: 'success',
      });
      if (payload[0].opr === 'CREATE') {
        navigation.navigate('EditExperienceItem', {
          experienceId: response[0],
        });
      } else {
        navigation.goBack();
      }
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Ship',
            description: 'Unable to save Ship',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
      if (refetch) {
        refetch();
      }
      setIsSubmitted(false);
    }
  };

  const populateData = useCallback(
    async (imoData: { imo: string; dataType: string }) => {
      try {
        const response = await fetchShipDetails({
          imo: imoData.imo,
          dataType: imoData.dataType,
        });

        methods.setValue('grossTonnage', response.gt.toString());
        methods.setValue('deadWeight', response.dwt.toString());
      } catch (error) {}
    },
    [methods],
  );

  const handlePresentCheckbox = () => {
    setIsPresent(!isPresent);
    methods.setValue('toDate', null);
  };

  const handleAddEquipmentCargo = () => {
    const payload = generateShipAddEditPayload(
      preFilledData,
      methods.watch(),
      shipId,
      field,
      localSkills,
      initialSkills,
    );
    return payload;
  };

  const clearFields = () => {
    dispatch(clearSelection('ship'));
    dispatch(clearSelection('shipType'));
    dispatch(clearSelection('department'));
    setLocalSkills([]);
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    navigation,
    localSkills,
    setLocalSkills,
    handlePresentCheckbox,
    isPresent,
    populateData,
    isAddVisible,
    clearFields,
    handleAddEquipmentCargo,
    equipments,
    shipReFetch,
    loading,
    isSubmitted,
    setIsSubmitted,
  };
};
