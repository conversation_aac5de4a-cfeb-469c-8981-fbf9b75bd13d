import { Dispatch, SetStateAction } from 'react';
import { StackNavigationProp } from '@react-navigation/stack';
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdNameI, IdTypeI } from '@/src/types/common/data';
import { ProfileStackParamsListI } from '@/src/navigation/types';

export interface EditShipItemPropsI {
  onBack: () => void;
  profileId: string;
  experienceId?: string;
  shipId?: string;
  preFilledData: ShipPreFilledDataTypeI[];
  field: FieldTypeI;
  fromProfileExperience: {
    id: string;
    imo: string;
  };
  refetch: () => void;
}

export interface UseEditShipItemI {
  methods: UseFormReturn<ShipDetailsFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: ShipDetailsFormDataI) => Promise<void>;
  // onAddSkill: (skill: string) => void;
  // onRemoveSkill: (skill: string) => void;
  navigation: StackNavigationProp<ProfileStackParamsListI>;
  localSkills: IdNameI[];
  setLocalSkills: Dispatch<SetStateAction<SearchResultI[]>>;
  handlePresentCheckbox: () => void;
  isPresent: boolean;
  populateData: (imoData: { imo: string; dataType: string }) => Promise<void>;
  isAddVisible: boolean;
  clearFields: () => void;
  handleAddEquipmentCargo: () => ShipCreateEditPayloadI[];
  equipments: ShipEquipmentI[];
  shipReFetch: () => void;
  loading: boolean;
  isSubmitted: boolean;
  setIsSubmitted: Dispatch<SetStateAction<boolean>>;
}

export type ShipDetailsFormDataI = {
  imoNumber: ImoTypeI;
  shipName: string;
  shipSize: string;
  shipType: SearchResultI;
  power: string;
  capacity: string;
  fromDate: string;
  toDate: string | null;
  deadWeight: string;
  grossTonnage: string;
  additionalDetails: string | null;
  department: SearchResultI;
};

export type ShipTabsI = {
  equipment: React.ReactNode;
  cargo: React.ReactNode;
};

export type ShipEntitySearchResultI = {
  dataType: string;
  imageUrl: string;
  imo: string;
  matchedName: string;
  name: string;
};

type ImoTypeI = {
  imo: string;
  dataType: string;
};

export type ShipTypeEntitySearchResultI = {
  dataType: 'raw' | 'master';
  capacityUnitType: string;
  id: string;
  name: string;
};

export type FieldTypeI = {
  designation: SearchResultI;
  fromDate: string;
  id: string;
  ships: FieldShipType[];
  toDate: string;
  experienceDesignationId?: string;
};

export type FieldShipType = {
  fromDate: string;
  id: string;
  name: string;
  ship: Omit<SearchResultI, 'id'> & { imo: string };
  subVesselType: SearchResultI;
  toDate: string;
};

export type ShipEquipmentI = {
  category: SearchResultI;
  manufacturerName: string;
  model: string;
  id: string;
};

export type ShipPayloadI = {
  opr?: 'UPDATE' | 'CREATE' | 'DELETE' | 'NESTED_OPR';
  ship?: ImoTypeI;
  name?: string;
  sizeGt?: number;
  powerKw?: number;
  fromDate?: string;
  toDate?: string | null;
  details?: string;
  department?: IdTypeI;
  subVesselType?: IdTypeI;
  dwt?: number;
  id?: string;
  skills?: {
    opr: string;
    id: string;
    dataType: 'raw' | 'master';
  }[];
};

export type ShipCreateEditPayloadI = {
  opr: 'UPDATE' | 'CREATE' | 'DELETE' | 'NESTED_OPR';
  entity?: SearchResultI;
  id?: string;
  designations: {
    designation?: SearchResultI;
    fromDate?: string;
    id?: string;
    opr?: string;
    ships?: ShipPayloadI[];
    toDate?: string;
  }[];
};

export type ShipPreFilledDataTypeI = {
  opr: 'UPDATE' | 'CREATE' | 'DELETE' | 'NESTED_OPR';
  entity: SearchResultI;
  designations: {
    designation: SearchResultI;
    fromDate: string;
    id: string;
    opr: string;
    toDate: string;
  }[];
};
