import React from 'react';
import { View, TextInput, Pressable, Platform } from 'react-native';
import Send from '@/src/assets/svgs/Send';
import type { MessageInputProps } from './types';
import { useMessageInput } from './useMessageInput';

export const MessageInput: React.FC<MessageInputProps> = ({
  value,
  onChangeText,
  onSend,
  loading,
}) => {
  const { handleKeyPress } = useMessageInput(onSend);

  return (
    <View className="border-t border-gray-200 bg-white py-1">
      <View className="flex-row items-center px-3 py-2">
        <View className="flex-1 flex-row items-center border border-borderLight bg-whitePink rounded-full px-3 py-1 max-h-24">
          <TextInput
            value={value}
            onChangeText={onChangeText}
            placeholder="Type a message"
            placeholderTextColor="#6B7280"
            className="flex-1 text-sm text-gray-900 p-0 max-h-14"
            multiline
            scrollEnabled
            textAlignVertical="top"
            style={{
              paddingVertical: Platform.OS === 'android' ? 4 : 8,
            }}
            editable={!loading}
            onKeyPress={handleKeyPress}
            returnKeyType="send"
            blurOnSubmit={Platform.OS === 'android'}
          />
          <Pressable
            className="p-2"
            onPress={onSend}
            disabled={loading || !value.trim()}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Send color={!value.trim() || loading ? '#6B7280' : '#448600'} width={2} height={2} />
          </Pressable>
        </View>
      </View>
    </View>
  );
};
