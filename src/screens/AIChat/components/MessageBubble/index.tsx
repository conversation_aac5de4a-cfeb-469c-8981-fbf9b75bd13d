import type React from 'react';
import { View, Text } from 'react-native';
import TypingDots from '../TypingDots';
import type { MessageBubbleProps } from './types';
import { useMessageBubble } from './useMessageBubble';

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isLoading,
  streamedText,
  currentAiMessageId,
}) => {
  const { formatMessageTime } = useMessageBubble();
  const isAI = message.from === 'ai';
  const isLastAI = isAI && isLoading && !message.text;
  const isCurrentStreaming = currentAiMessageId === message.id && streamedText;

  const displayText = isCurrentStreaming ? streamedText : message.text;

  return (
    <View className={`mb-2 flex-row items-end ${isAI ? 'justify-start' : 'justify-end'}`}>
      <View
        className={`max-w-[80%] p-3 rounded-xl ${isAI ? 'bg-gray-100 self-start' : 'bg-violet-100 self-end'}`}
      >
        {isLastAI ? (
          <View className="flex-row items-center">
            <TypingDots />
          </View>
        ) : (
          <Text className="text-sm text-gray-900">{displayText}</Text>
        )}
        <Text className="text-xs text-gray-400 mt-1 text-right">
          {formatMessageTime(new Date(message.timestamp))}
        </Text>
      </View>
    </View>
  );
};
