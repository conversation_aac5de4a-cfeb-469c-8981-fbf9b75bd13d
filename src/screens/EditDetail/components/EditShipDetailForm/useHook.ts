/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { z } from 'zod';
import { setSelection, clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { AppStackParamListI } from '@/src/navigation/types';
import { createShipAPI } from '@/src/networks/ship/profile';
import { CreateShipPayloadI } from '@/src/networks/ship/types';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { CategoryI, DetailScreenTypeI } from './types';

export const ShipImoR = /^\d{7}$/;
export const ShipR = /^[a-zA-Z0-9\s-'.()]+$/;
export const ShipMmsiR = /^\d{9}$/;
export const ShipCallSignR = /^[A-Z][A-Z0-9]*$/;
export const CountryISO2R = /^[A-Z]{2}$/;

export const shipDetailSchema = z.object({
  imo: z
    .string()
    .length(7, 'IMO must be exactly 7 digits')
    .regex(ShipImoR, 'IMO must contain only digits'),
  name: z
    .string()
    .min(1, 'Name is required')
    .max(150, 'Name must not exceed 150 characters')
    .regex(ShipR, 'Name contains invalid characters'),
  flag: z
    .string()
    .optional(),
  mmsi: z
    .string()
    .optional()
    .refine((val) => !val || (val.length === 9 && ShipMmsiR.test(val)), {
      message: 'MMSI must be exactly 9 digits',
    }),
  callSign: z
    .string()
    .optional()
    .refine((val) => !val || ShipCallSignR.test(val), {
      message:
        'Call Sign must start with a capital letter and contain only capital letters and numbers (no spaces)',
    }),
  shipType: z
    .string()
    .optional(),
});

export type ShipDetailFormData = z.infer<typeof shipDetailSchema>;

export const useEditShipDetails = (type: DetailScreenTypeI, text: string) => {
  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const flagSelection = useSelector(selectSelectionByKey('country')) as SearchResultI;
  const shipTypeSelection = useSelector(selectSelectionByKey('shipType')) as SearchResultI;

  const defaultValues: ShipDetailFormData = {
    imo: text,
    name: '',
    flag: '',
    mmsi: '',
    callSign: '',
    shipType: '',
  };

  const [loading, setLoading] = useState(false);
  const methods = useForm<ShipDetailFormData>({
    resolver: zodResolver(shipDetailSchema),
    mode: 'onChange',
    defaultValues,
  });

  useEffect(() => {

    dispatch(clearSelection('country'));
    dispatch(clearSelection('shipType'));

  }, [dispatch]);

  const onSubmit = async (data: ShipDetailFormData) => {
    try {
      setLoading(true);

      const payload: CreateShipPayloadI = {
        imo: data.imo,
        name: data.name,
        status: 'ACTIVE',
        ...(data.mmsi && { mmsi: data.mmsi }),
        ...(data.callSign && { callSign: data.callSign }),
        ...(flagSelection && { flagCountryIso2: flagSelection.id }),
        ...(shipTypeSelection && {
          mainVesselType: {
            id: shipTypeSelection.id,
            dataType: shipTypeSelection.dataType,
          }
        }),
      };

      const result = await createShipAPI(payload);

      showToast({
        type: 'success',
        message: 'Ship Added Successfully',
        description: `${result.name} has been added to the database`,
      });

      dispatch(
        setSelection({
          key: 'ship',
          value: {
            id: result.imo,
            name: result.name,
            dataType: result.dataType,
          },
        }),
      );

      dispatch(clearSelection('country'));
      dispatch(clearSelection('shipType'));

      navigation.goBack();
    } catch (err) {
      handleError(err, {
        handle4xxError: () => {
          showToast({
            type: 'error',
            message: 'Failed to Add Ship',
            description: 'Please check your input and try again',
          });
        },
        handle5xxError: () => {
          showToast({
            type: 'error',
            message: 'Server Error',
            description: 'Please try again later',
          });
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const categories: CategoryI[] = [
    {
      id: '1',
      key: 'imo',
      title: 'IMO',
    },
    {
      id: '2',
      key: 'name',
      title: 'Name',
    },
    {
      id: '3',
      key: 'flag',
      title: 'Flag',
    },
    {
      id: '4',
      key: 'shipType',
      title: 'Ship Type',
    },
    {
      id: '5',
      key: 'mmsi',
      title: 'MMSI',
    },
    {
      id: '6',
      key: 'callSign',
      title: 'Call Sign',
    },
  ];

  return { methods, onSubmit, loading, categories, flagSelection, shipTypeSelection };
};
