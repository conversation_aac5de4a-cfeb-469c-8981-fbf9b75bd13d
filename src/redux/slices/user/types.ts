/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { SearchResultI } from '../entitysearch/types';

export type FetchProfileResultI = {
  email: string;
  name: string;
  username: string;
  avatar: string | null;
  profileId: string;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
};

export interface UserState {
  username: string;
  email: string;
  profileId: string;
  fullName: string;
  gender: string;
  avatar: string | null;
  country?: SearchResultI;
  organisation?: SearchResultI;
  designation?: SearchResultI;
  isUsernameSaved: boolean;
  isEmailVerified: boolean;
  isPersonalDetailsSaved: boolean;
  isWorkDetailsSaved: boolean;
  isAuthenticated: boolean;
  token: string;
  loading: boolean;
  error: string | null;
}
