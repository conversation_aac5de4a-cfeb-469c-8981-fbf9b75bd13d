/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import AppError from '@/src/errors/networks/AppError';
import { loginAPI } from '@/src/networks/auth/login';
import { logoutAPI } from '@/src/networks/auth/logout';
import { updateUsernameAPI } from '@/src/networks/profile/username';
import { fetchProfileAPI, editUserProfileAPI } from '@/src/networks/profile/userProfile';
import { SearchResultI } from '../entitysearch/types';
import { UserState, FetchProfileResultI } from './types';

interface LoginResponse {
  name: string;
  email: string;
  username: string;
  profileId: string;
  avatar: string | null;
  isEmailVerified: boolean;
  isUsernameSaved: boolean;
  isPersonalDetailsSaved: boolean;
  isWorkDetailsSaved: boolean;
  token: string;
}

const initialState: UserState = {
  username: '',
  email: '',
  profileId: '',
  fullName: '',
  gender: '',
  avatar: null,
  country: undefined,
  organisation: undefined,
  designation: undefined,
  isUsernameSaved: false,
  isEmailVerified: false,
  isPersonalDetailsSaved: false,
  isWorkDetailsSaved: false,
  isAuthenticated: false,
  token: '',
  loading: false,
  error: null,
};

export const createAccountAsync = createAsyncThunk<
  {
    username: string;
    email: string;
    profileId: string;
    isEmailVerified: boolean;
    token: string;
  },
  { username: string; email: string; password: string },
  { rejectValue: string }
>('user/createAccount', async ({ username, email }, { rejectWithValue }) => {
  try {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return {
      username,
      email,
      profileId: `user-${Date.now()}`,
      isEmailVerified: false,
      token: `token-${Date.now()}`,
    };
  } catch (error) {
    return rejectWithValue('Failed to create account');
  }
});

export const signInAsync = createAsyncThunk<
  LoginResponse,
  { email: string; password: string; deviceToken: string },
  { rejectValue: string }
>('user/signIn', async ({ email, password, deviceToken }, { rejectWithValue }) => {
  try {
    const loginResult = await loginAPI({
      type: 'EMAIL_PASSWORD',
      email,
      password,
      deviceToken,
    });
    if (!loginResult?.token) {
      throw new AppError('Invalid email or password');
    }
    return loginResult;
  } catch (error: unknown) {
    return rejectWithValue((error as Error).message);
  }
});

export const googleSignInAsync = createAsyncThunk<
  LoginResponse,
  { googleToken: string; deviceToken: string },
  { rejectValue: string }
>('user/googleSignIn', async ({ googleToken, deviceToken }, { rejectWithValue }) => {
  try {
    const loginResult = await loginAPI({
      type: 'GOOGLE',
      googleToken,
      deviceToken,
    });
    if (!loginResult?.token) {
      throw new AppError('Google sign-in failed');
    }
    if (!loginResult.email) {
      throw new AppError('Email not provided in login response');
    }
    return loginResult;
  } catch (error: unknown) {
    return rejectWithValue((error as Error).message);
  }
});

export const signOutAsync = createAsyncThunk('user/logout', async (_, { rejectWithValue }) => {
  try {
    await logoutAPI();
  } catch (error: unknown) {
    return rejectWithValue((error as Error).message);
  }
});

export const saveUsernameAsync = createAsyncThunk<
  { username: string },
  { username: string },
  { rejectValue: string }
>('user/saveUsername', async ({ username }, { rejectWithValue }) => {
  try {
    await updateUsernameAPI({ username });
    return { username };
  } catch (error) {
    return rejectWithValue('Failed to save username');
  }
});

export const setProfileAsync = createAsyncThunk<
  {
    fullName: string;
    gender: string;
    country?: SearchResultI;
    organisation?: SearchResultI;
    designation?: SearchResultI;
  },
  {
    fullName: string;
    gender: string;
    country?: SearchResultI;
    organisation?: SearchResultI;
    designation?: SearchResultI;
  },
  { rejectValue: string }
>('user/setProfile', async (profileData, { rejectWithValue }) => {
  try {
    return profileData;
  } catch (error) {
    return rejectWithValue('Failed to set profile');
  }
});

export const fetchAndSaveUserProfile = createAsyncThunk<
  FetchProfileResultI,
  { id: string },
  { rejectValue: string }
>('user/fetchProfile', async ({ id }, { rejectWithValue }) => {
  try {
    const response = await fetchProfileAPI(id);
    return response;
  } catch (error) {
    return rejectWithValue('Failed to fetch profile');
  }
});

export const updateUserProfileAsync = createAsyncThunk<
  { name?: string; designation?: SearchResultI; entity?: SearchResultI; avatar: string | null },
  { name?: string; designation?: SearchResultI; entity?: SearchResultI; avatar: string | null },
  { rejectValue: string }
>('user/updateUserProfile', async (profileData, { rejectWithValue }) => {
  try {
    await editUserProfileAPI(profileData);
    return profileData;
  } catch (error) {
    return rejectWithValue('Failed to update profile');
  }
});

const updateAuthenticationStatus = (state: UserState) => {
  state.isAuthenticated =
    !!state.token &&
    !!state.username &&
    !!state.email &&
    !!state.profileId &&
    state.isUsernameSaved &&
    state.isPersonalDetailsSaved &&
    state.isWorkDetailsSaved;
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    resetUserState: () => initialState,
    updateWorkDetail: (state) => {
      state.isWorkDetailsSaved = true;
      updateAuthenticationStatus(state);
    },
    updateProfileDetail: (state) => {
      state.isPersonalDetailsSaved = true;
      updateAuthenticationStatus(state);
    },
    updateUserProfile: (
      state,
      action: PayloadAction<{
        name?: string;
        designation?: SearchResultI;
        entity?: SearchResultI;
        avatar: string | null;
      }>,
    ) => {
      const { name, designation, entity, avatar } = action.payload;
      if (name !== undefined) state.fullName = name;
      if (designation !== undefined) state.designation = designation;
      if (entity !== undefined) state.organisation = entity;
      state.avatar = avatar;
    },
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    updateAvatar: (state, action: PayloadAction<string | null>) => {
      state.avatar = action.payload;
    },
    verifyEmail: (state) => {
      state.isEmailVerified = true;
      updateAuthenticationStatus(state);
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createAccountAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createAccountAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.profileId = action.payload.profileId;
        state.email = action.payload.email;
        state.username = action.payload.username;
        state.isEmailVerified = action.payload.isEmailVerified;
        state.isUsernameSaved = true;
        state.token = action.payload.token;
        updateAuthenticationStatus(state);
      })
      .addCase(createAccountAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to create account';
      })
      .addCase(signInAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(signInAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.email = action.payload.email;
        state.username = action.payload.username;
        state.profileId = action.payload.profileId;
        state.avatar = action.payload.avatar;
        state.isEmailVerified = action.payload.isEmailVerified;
        state.isUsernameSaved = action.payload.isUsernameSaved;
        state.isPersonalDetailsSaved = action.payload.isPersonalDetailsSaved;
        state.isWorkDetailsSaved = action.payload.isWorkDetailsSaved;
        state.token = action.payload.token;
        updateAuthenticationStatus(state);
      })
      .addCase(signInAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Sign in failed';
      })
      .addCase(googleSignInAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(googleSignInAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.email = action.payload.email;
        state.username = action.payload.username;
        state.profileId = action.payload.profileId;
        state.avatar = action.payload.avatar;
        state.isEmailVerified = action.payload.isEmailVerified;
        state.isUsernameSaved = action.payload.isUsernameSaved;
        state.isPersonalDetailsSaved = action.payload.isPersonalDetailsSaved;
        state.isWorkDetailsSaved = action.payload.isWorkDetailsSaved;
        state.token = action.payload.token;
        updateAuthenticationStatus(state);
      })
      .addCase(googleSignInAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Google sign in failed';
      })
      .addCase(signOutAsync.fulfilled, (state) => {
        Object.assign(state, initialState);
      })
      .addCase(saveUsernameAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(saveUsernameAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.username = action.payload.username;
        state.isUsernameSaved = true;
        updateAuthenticationStatus(state);
      })
      .addCase(saveUsernameAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to save username';
      })
      .addCase(setProfileAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(setProfileAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.fullName = action.payload.fullName;
        state.gender = action.payload.gender;
        state.country = action.payload.country;
        state.organisation = action.payload.organisation;
        state.designation = action.payload.designation;
        updateAuthenticationStatus(state);
      })
      .addCase(setProfileAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to set profile';
      })
      .addCase(fetchAndSaveUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAndSaveUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.fullName = action.payload.name;
        state.avatar = action.payload.avatar;
        state.country = undefined;

        state.designation = action.payload.designation
          ? {
              id: action.payload.designation.id,
              name: action.payload.designation.name,
              dataType: action.payload.designation.dataType,
            }
          : undefined;

        state.organisation = action.payload.entity
          ? {
              id: action.payload.entity.id,
              name: action.payload.entity.name,
              dataType: action.payload.entity.dataType,
            }
          : undefined;

        updateAuthenticationStatus(state);
      })
      .addCase(fetchAndSaveUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch profile';
      })
      .addCase(updateUserProfileAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserProfileAsync.fulfilled, (state, action) => {
        state.loading = false;
        const { name, designation, entity } = action.payload;
        if (name !== undefined) state.fullName = name;
        if (designation !== undefined) state.designation = designation;
        if (entity !== undefined) state.organisation = entity;
      })
      .addCase(updateUserProfileAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to update profile';
      });
  },
});

export const {
  resetUserState,
  updateWorkDetail,
  updateProfileDetail,
  updateUserProfile,
  clearError,
  setLoading,
  updateAvatar,
  verifyEmail,
} = userSlice.actions;

export default userSlice.reducer;
