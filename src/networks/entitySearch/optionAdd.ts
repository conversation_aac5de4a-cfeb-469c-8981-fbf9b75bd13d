import { apiCall } from '../../services/api';
import { OptionAddBodyI, OptionAddResultI } from './types';

export const optionAddAPI = async (
  collection: string,
  payload: OptionAddBodyI,
): Promise<OptionAddResultI> => {
  const route = collection.startsWith('designation-')
    ? '/backend/api/v1/company/designation/options'
    : collection === 'entity'
      ? '/backend/api/v1/company/entity/all/options'
      : collection === 'shipType'
        ? '/backend/api/v1/ship/sub-vessel-type/options'
        : `/backend/api/v1/company/${collection}/options`;


  return apiCall<OptionAddBodyI, OptionAddResultI>(route, 'POST', {
    isAuth: true,
    payload,
  });
};
