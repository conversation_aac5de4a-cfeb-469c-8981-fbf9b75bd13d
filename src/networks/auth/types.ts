/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

export type AuthLoginBodyI = {
  email?: string;
  password?: string;
  type: 'GOOGLE' | 'EMAIL_PASSWORD';
  googleToken?: string;
  deviceToken: string;
};

export type AuthLoginResultI = {
  name: string;
  username: string;
  email: string;
  profileId: string;
  avatar: string | null;
  isUsernameSaved: boolean;
  isEmailVerified: boolean;
  isPersonalDetailsSaved: boolean;
  isWorkDetailsSaved: boolean;
  token: string;
};
